"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { Mail, Phone, MapPin, Send, MessageCircle, User, AtSign } from "lucide-react"

interface ContactLoadingScreenProps {
  onComplete: () => void
}

const ContactLoadingScreen = ({ onComplete }: ContactLoadingScreenProps) => {
  const [progress, setProgress] = useState(0)
  const [activeContact, setActiveContact] = useState(0)
  const [messageIndex, setMessageIndex] = useState(0)
  
  const contactMethods = [
    { icon: Mail, label: "Email", value: "<EMAIL>", color: "text-red-500" },
    { icon: Phone, label: "Phone", value: "+91 XXXXX XXXXX", color: "text-green-500" },
    { icon: MapPin, label: "Location", value: "India", color: "text-blue-500" },
    { icon: MessageCircle, label: "Message", value: "Let's connect!", color: "text-purple-500" }
  ]

  const messages = [
    "Establishing connection...",
    "Loading contact information...",
    "Preparing communication channels...",
    "Ready to connect!"
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(onComplete, 500)
          return 100
        }
        return prev + Math.random() * 9
      })
    }, 140)

    return () => clearInterval(timer)
  }, [onComplete])

  useEffect(() => {
    const contactTimer = setInterval(() => {
      setActiveContact((prev) => (prev + 1) % contactMethods.length)
    }, 700)

    return () => clearInterval(contactTimer)
  }, [])

  useEffect(() => {
    const messageTimer = setInterval(() => {
      setMessageIndex((prev) => (prev + 1) % messages.length)
    }, 1000)

    return () => clearInterval(messageTimer)
  }, [])

  return (
    <div className="fixed inset-0 bg-gradient-to-tr from-background via-primary-orange/5 to-background z-50 flex items-center justify-center">
      {/* Communication Waves Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-96 h-96 border border-primary-orange/20 rounded-full animate-ping"></div>
          <div className="absolute inset-8 border border-primary-blue/20 rounded-full animate-ping delay-500"></div>
          <div className="absolute inset-16 border border-primary-orange/10 rounded-full animate-ping delay-1000"></div>
        </div>
      </div>

      <div className="text-center space-y-8 relative z-10">
        {/* Logo with Message Bubble */}
        <div className="relative w-32 h-32 mx-auto">
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary-orange/20 to-primary-blue/20 animate-pulse"></div>
          <div className="absolute inset-2 rounded-full bg-card border border-border flex items-center justify-center">
            <div className="relative w-16 h-16">
              <Image src="/images/rs-logo.svg" alt="RS Logo" fill className="object-contain" />
            </div>
          </div>
          {/* Message Bubble */}
          <div className="absolute -top-2 -right-2 p-2 bg-primary-orange rounded-full animate-bounce">
            <Send className="w-4 h-4 text-white" />
          </div>
        </div>

        {/* Loading Text */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-foreground">Loading Contact...</h2>
          <p className="text-muted-foreground">{messages[messageIndex]}</p>

          {/* Progress Bar with Message Style */}
          <div className="w-80 mx-auto">
            <div className="bg-card border border-border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <User className="w-4 h-4 text-primary-blue" />
                <span className="text-sm text-foreground">Rahul Seervi</span>
                <div className="flex-1"></div>
                <div className="text-xs text-muted-foreground">Online</div>
              </div>
              
              <div className="w-full h-2 bg-muted rounded-full overflow-hidden mb-2">
                <div
                  className="h-full bg-gradient-to-r from-primary-orange to-primary-blue transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
              
              <div className="text-xs text-muted-foreground text-right">
                {Math.round(progress)}% loaded
              </div>
            </div>
          </div>
        </div>

        {/* Contact Methods Animation */}
        <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
          {contactMethods.map((contact, index) => {
            const IconComponent = contact.icon
            return (
              <div
                key={index}
                className={`p-4 rounded-lg border transition-all duration-300 ${
                  activeContact === index 
                    ? 'bg-primary-orange/10 border-primary-orange scale-105 shadow-lg' 
                    : 'bg-card border-border'
                }`}
              >
                <div className="flex flex-col items-center space-y-2">
                  <div className={`p-2 rounded-full ${
                    activeContact === index ? 'bg-primary-orange/20' : 'bg-muted/50'
                  }`}>
                    <IconComponent className={`w-5 h-5 ${
                      activeContact === index ? contact.color : 'text-muted-foreground'
                    }`} />
                  </div>
                  <div className="text-center">
                    <div className="text-xs font-medium text-foreground">{contact.label}</div>
                    <div className="text-xs text-muted-foreground truncate max-w-20">
                      {contact.value}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Connection Status */}
        <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Ready to receive messages</span>
        </div>
      </div>
    </div>
  )
}

export default ContactLoadingScreen
