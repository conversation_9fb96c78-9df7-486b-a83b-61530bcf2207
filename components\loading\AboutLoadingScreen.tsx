"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { GraduationCap, Award, Calendar, MapPin } from "lucide-react"

interface AboutLoadingScreenProps {
  onComplete: () => void
}

const AboutLoadingScreen = ({ onComplete }: AboutLoadingScreenProps) => {
  const [progress, setProgress] = useState(0)
  const [activeIcon, setActiveIcon] = useState(0)
  const icons = [
    { icon: GraduationCap, label: "Education", color: "text-blue-500" },
    { icon: Award, label: "Achievements", color: "text-yellow-500" },
    { icon: Calendar, label: "Experience", color: "text-green-500" },
    { icon: MapPin, label: "Journey", color: "text-purple-500" }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(onComplete, 500)
          return 100
        }
        return prev + Math.random() * 10
      })
    }, 150)

    return () => clearInterval(timer)
  }, [onComplete])

  useEffect(() => {
    const iconTimer = setInterval(() => {
      setActiveIcon((prev) => (prev + 1) % icons.length)
    }, 600)

    return () => clearInterval(iconTimer)
  }, [])

  return (
    <div className="fixed inset-0 bg-gradient-to-tr from-background via-primary-blue/5 to-background z-50 flex items-center justify-center">
      {/* Timeline Background */}
      <div className="absolute left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-primary-blue/30 to-transparent"></div>
      
      <div className="text-center space-y-8 relative z-10">
        {/* Logo with Timeline Effect */}
        <div className="relative w-28 h-28 mx-auto">
          <div className="absolute inset-0 rounded-full border-2 border-primary-blue/20 animate-ping"></div>
          <div className="absolute inset-2 rounded-full bg-card border border-border flex items-center justify-center">
            <div className="relative w-16 h-16">
              <Image src="/images/rs-logo.svg" alt="RS Logo" fill className="object-contain" />
            </div>
          </div>
        </div>

        {/* Loading Text */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-foreground">Loading About Me...</h2>
          <p className="text-muted-foreground">Discovering my journey and experiences</p>

          {/* Progress Bar with Timeline Style */}
          <div className="w-72 h-2 bg-muted rounded-full overflow-hidden mx-auto relative">
            <div
              className="h-full bg-gradient-to-r from-primary-blue to-primary-orange transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-primary-blue rounded-full animate-pulse"></div>
          </div>

          {/* Progress Percentage */}
          <p className="text-muted-foreground text-sm">{Math.round(progress)}%</p>
        </div>

        {/* Animated Icons */}
        <div className="flex justify-center space-x-6">
          {icons.map((item, index) => {
            const IconComponent = item.icon
            return (
              <div
                key={index}
                className={`p-4 rounded-full transition-all duration-300 ${
                  activeIcon === index 
                    ? 'bg-primary-blue/20 scale-110 shadow-lg' 
                    : 'bg-muted/30 scale-100'
                }`}
              >
                <IconComponent 
                  className={`w-6 h-6 transition-colors duration-300 ${
                    activeIcon === index ? item.color : 'text-muted-foreground'
                  }`} 
                />
              </div>
            )
          })}
        </div>

        {/* Current Loading Stage */}
        <div className="text-sm text-muted-foreground">
          Loading {icons[activeIcon].label.toLowerCase()}...
        </div>
      </div>
    </div>
  )
}

export default AboutLoadingScreen
