"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Download, ArrowRight, Github, Linkedin, Mail, Code, Database, Smartphone } from "lucide-react"
import HomeLoadingScreen from "@/components/loading/HomeLoadingScreen"

export default function Home() {
  const [loading, setLoading] = useState(true)
  const [text, setText] = useState("")
  const fullText = "B.Tech CSE Student | Big Data Analytics Specialist"

  useEffect(() => {
    if (!loading) {
      let index = 0
      const timer = setInterval(() => {
        setText(fullText.slice(0, index))
        index++
        if (index > fullText.length) {
          clearInterval(timer)
        }
      }, 100)

      return () => clearInterval(timer)
    }
  }, [loading])

  const handleDownloadResume = () => {
    const link = document.createElement("a")
    link.href = "/resume.pdf"
    link.download = "Rahul_Seervi_Resume.pdf"
    link.click()
  }

  const quickStats = [
    { icon: <Code className="w-6 h-6" />, label: "Projects", value: "15+" },
    { icon: <Database className="w-6 h-6" />, label: "Technologies", value: "10+" },
    { icon: <Smartphone className="w-6 h-6" />, label: "Experience", value: "2+ Years" },
  ]

  if (loading) {
    return <HomeLoadingScreen onComplete={() => setLoading(false)} />
  }

  return (
    <div className="min-h-screen bg-background text-foreground page-transition">
      {/* Hero Section */}
      <section className="min-h-screen flex items-center justify-center relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-20 left-20 w-72 h-72 bg-primary-orange/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-primary-blue/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="container-custom section-padding text-center">
          <div className="animate-fade-in">
            {/* Profile Image */}
            <div className="mb-8">
              <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto rounded-full bg-gradient-to-r from-primary-orange to-primary-blue p-1">
                <div className="w-full h-full rounded-full bg-card flex items-center justify-center">
                  <Image src="/images/rs-logo.svg" alt="RS Logo" width={60} height={60} className="object-contain" />
                </div>
              </div>
            </div>

            {/* Main Content */}
            <h1 className="text-3xl sm:text-5xl md:text-7xl font-bold mb-6">
              Hi, I'm <span className="gradient-text">Rahul Seervi</span>
            </h1>

            <div className="h-12 sm:h-16 mb-8">
              <p className="text-lg sm:text-xl md:text-2xl text-muted-foreground">
                {text}
                <span className="animate-pulse">|</span>
              </p>
            </div>

            <p className="text-base sm:text-lg text-muted-foreground mb-8 sm:mb-12 max-w-2xl mx-auto px-4">
              Passionate about creating innovative solutions with Java, React, and modern web technologies. Currently
              exploring the fascinating world of Big Data Analytics.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8 sm:mb-12 px-4">
              <button onClick={handleDownloadResume} className="btn-primary flex items-center justify-center gap-2">
                <Download className="w-4 h-4 sm:w-5 sm:h-5" />
                Download Resume
              </button>
              <Link href="/projects" className="btn-secondary flex items-center justify-center gap-2">
                View Projects
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </Link>
            </div>

            {/* Social Links */}
            <div className="flex justify-center space-x-4 sm:space-x-6 mb-8 sm:mb-12">
              <a
                href="https://github.com/rahulseervi"
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 rounded-full bg-muted hover:bg-primary-orange hover:text-white transition-all duration-300 hover:scale-110"
              >
                <Github className="w-5 h-5 sm:w-6 sm:h-6" />
              </a>
              <a
                href="https://linkedin.com/in/rahulseervi"
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 rounded-full bg-muted hover:bg-primary-blue hover:text-white transition-all duration-300 hover:scale-110"
              >
                <Linkedin className="w-5 h-5 sm:w-6 sm:h-6" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="p-3 rounded-full bg-muted hover:bg-primary-orange hover:text-white transition-all duration-300 hover:scale-110"
              >
                <Mail className="w-5 h-5 sm:w-6 sm:h-6" />
              </a>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 max-w-2xl mx-auto px-4">
              {quickStats.map((stat, index) => (
                <div key={index} className="text-center p-4 rounded-lg bg-card/50 backdrop-blur-sm border">
                  <div className="text-primary-orange mb-2 flex justify-center">{stat.icon}</div>
                  <div className="text-xl sm:text-2xl font-bold text-foreground">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Quick Navigation */}
      <section className="section-padding bg-muted/30">
        <div className="container-custom">
          <h2 className="text-2xl sm:text-3xl font-bold text-center mb-8 sm:mb-12">Explore My Work</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {[
              { title: "About Me", href: "/about", description: "Learn about my journey" },
              { title: "Skills", href: "/skills", description: "Technical expertise" },
              { title: "Projects", href: "/projects", description: "Featured work" },
              { title: "Contact", href: "/contact", description: "Get in touch" },
            ].map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="group p-6 rounded-xl bg-card hover:bg-accent transition-all duration-300 card-hover border"
              >
                <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-2 group-hover:text-primary-orange transition-colors duration-300">
                  {item.title}
                </h3>
                <p className="text-muted-foreground text-sm">{item.description}</p>
                <ArrowRight className="w-5 h-5 text-primary-orange mt-4 group-hover:translate-x-2 transition-transform duration-300" />
              </Link>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
