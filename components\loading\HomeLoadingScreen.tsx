"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { Code, Database, Smartphone } from "lucide-react"

interface HomeLoadingScreenProps {
  onComplete: () => void
}

const HomeLoadingScreen = ({ onComplete }: HomeLoadingScreenProps) => {
  const [progress, setProgress] = useState(0)
  const [currentText, setCurrentText] = useState("")
  const texts = ["Welcome to my portfolio", "Loading experiences", "Preparing showcase", "Almost ready..."]
  const [textIndex, setTextIndex] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(onComplete, 500)
          return 100
        }
        return prev + Math.random() * 12
      })
    }, 120)

    return () => clearInterval(timer)
  }, [onComplete])

  useEffect(() => {
    const textTimer = setInterval(() => {
      setTextIndex((prev) => (prev + 1) % texts.length)
    }, 800)

    return () => clearInterval(textTimer)
  }, [])

  useEffect(() => {
    setCurrentText(texts[textIndex])
  }, [textIndex])

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-background via-background to-primary-blue/5 z-50 flex items-center justify-center">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-primary-orange/10 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-40 h-40 bg-primary-blue/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-3/4 left-1/2 w-24 h-24 bg-primary-orange/5 rounded-full blur-xl animate-pulse delay-500"></div>
      </div>

      <div className="text-center space-y-8 relative z-10">
        {/* Logo with Rotating Border */}
        <div className="relative w-32 h-32 mx-auto">
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary-orange to-primary-blue animate-spin opacity-20"></div>
          <div className="absolute inset-2 rounded-full bg-background flex items-center justify-center">
            <div className="relative w-20 h-20 animate-pulse-slow">
              <Image src="/images/rs-logo.svg" alt="RS Logo" fill className="object-contain" />
            </div>
          </div>
        </div>

        {/* Dynamic Loading Text */}
        <div className="space-y-4">
          <h2 className="text-3xl font-bold text-foreground transition-all duration-500">{currentText}</h2>
          <p className="text-muted-foreground">B.Tech CSE Student | Big Data Analytics Specialist</p>

          {/* Progress Bar */}
          <div className="w-80 h-3 bg-muted rounded-full overflow-hidden mx-auto">
            <div
              className="h-full bg-gradient-to-r from-primary-orange via-primary-blue to-primary-orange transition-all duration-300 ease-out animate-pulse"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Progress Percentage */}
          <p className="text-muted-foreground text-lg font-mono">{Math.round(progress)}%</p>
        </div>

        {/* Floating Icons */}
        <div className="flex justify-center space-x-8">
          <div className="p-3 rounded-full bg-muted/50 animate-bounce">
            <Code className="w-6 h-6 text-primary-orange" />
          </div>
          <div className="p-3 rounded-full bg-muted/50 animate-bounce delay-200">
            <Database className="w-6 h-6 text-primary-blue" />
          </div>
          <div className="p-3 rounded-full bg-muted/50 animate-bounce delay-400">
            <Smartphone className="w-6 h-6 text-primary-orange" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomeLoadingScreen
