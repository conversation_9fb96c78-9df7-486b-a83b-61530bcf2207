"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { Code, Database, Globe, Smartphone, Terminal, Wrench } from "lucide-react"

interface SkillsLoadingScreenProps {
  onComplete: () => void
}

const SkillsLoadingScreen = ({ onComplete }: SkillsLoadingScreenProps) => {
  const [progress, setProgress] = useState(0)
  const [skillBars, setSkillBars] = useState([0, 0, 0, 0, 0, 0])
  
  const skills = [
    { icon: Code, name: "Frontend", color: "from-blue-500 to-cyan-500" },
    { icon: Database, name: "Backend", color: "from-green-500 to-emerald-500" },
    { icon: Globe, name: "Web Dev", color: "from-purple-500 to-pink-500" },
    { icon: Smartphone, name: "Mobile", color: "from-orange-500 to-red-500" },
    { icon: Terminal, name: "DevOps", color: "from-gray-500 to-slate-600" },
    { icon: Wrench, name: "<PERSON><PERSON>", color: "from-yellow-500 to-amber-500" }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(onComplete, 500)
          return 100
        }
        return prev + Math.random() * 8
      })
    }, 100)

    return () => clearInterval(timer)
  }, [onComplete])

  useEffect(() => {
    const skillTimer = setInterval(() => {
      setSkillBars(prev => prev.map((bar, index) => {
        const target = Math.min(85 + Math.random() * 15, 100)
        return bar < target ? bar + Math.random() * 5 : target
      }))
    }, 150)

    return () => clearInterval(skillTimer)
  }, [])

  return (
    <div className="fixed inset-0 bg-gradient-to-bl from-background via-background to-primary-orange/5 z-50 flex items-center justify-center">
      {/* Code-like Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 text-xs font-mono text-foreground">
          {'{ "loading": true, "progress": ' + Math.round(progress) + '% }'}
        </div>
        <div className="absolute bottom-10 right-10 text-xs font-mono text-foreground">
          {'console.log("Loading skills...");'}
        </div>
      </div>

      <div className="text-center space-y-8 relative z-10">
        {/* Logo with Hexagonal Border */}
        <div className="relative w-32 h-32 mx-auto">
          <div className="absolute inset-0">
            <svg className="w-full h-full animate-spin-slow" viewBox="0 0 100 100">
              <polygon 
                points="50,5 85,25 85,75 50,95 15,75 15,25" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="1" 
                className="text-primary-orange opacity-30"
              />
            </svg>
          </div>
          <div className="absolute inset-4 rounded-full bg-card border border-border flex items-center justify-center">
            <div className="relative w-16 h-16">
              <Image src="/images/rs-logo.svg" alt="RS Logo" fill className="object-contain" />
            </div>
          </div>
        </div>

        {/* Loading Text */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-foreground">Loading Skills...</h2>
          <p className="text-muted-foreground font-mono">Compiling technical expertise</p>

          {/* Main Progress Bar */}
          <div className="w-80 h-3 bg-muted rounded-full overflow-hidden mx-auto">
            <div
              className="h-full bg-gradient-to-r from-primary-orange to-primary-blue transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Progress Percentage */}
          <p className="text-muted-foreground text-sm font-mono">[{Math.round(progress)}%] Loading complete</p>
        </div>

        {/* Skill Bars Animation */}
        <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
          {skills.map((skill, index) => {
            const IconComponent = skill.icon
            return (
              <div key={index} className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-muted/50">
                  <IconComponent className="w-4 h-4 text-foreground" />
                </div>
                <div className="flex-1">
                  <div className="text-xs text-muted-foreground mb-1">{skill.name}</div>
                  <div className="w-full h-1.5 bg-muted rounded-full overflow-hidden">
                    <div
                      className={`h-full bg-gradient-to-r ${skill.color} transition-all duration-500 ease-out`}
                      style={{ width: `${skillBars[index]}%` }}
                    />
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default SkillsLoadingScreen
