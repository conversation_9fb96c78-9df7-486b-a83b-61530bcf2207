"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { Folder, FileCode, GitBranch, Star, ExternalLink, Github } from "lucide-react"

interface ProjectsLoadingScreenProps {
  onComplete: () => void
}

const ProjectsLoadingScreen = ({ onComplete }: ProjectsLoadingScreenProps) => {
  const [progress, setProgress] = useState(0)
  const [currentProject, setCurrentProject] = useState(0)
  const [loadingStage, setLoadingStage] = useState(0)
  
  const projects = [
    { name: "UNO Game", tech: "Java", icon: FileCode },
    { name: "Traffic Analysis", tech: "Python", icon: GitBranch },
    { name: "AI Chatbot", tech: "React", icon: Star },
    { name: "University System", tech: "Full Stack", icon: Folder }
  ]

  const stages = [
    "Initializing repository...",
    "Loading project files...",
    "Compiling source code...",
    "Running build process...",
    "Finalizing setup..."
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(onComplete, 500)
          return 100
        }
        return prev + Math.random() * 6
      })
    }, 120)

    return () => clearInterval(timer)
  }, [onComplete])

  useEffect(() => {
    const projectTimer = setInterval(() => {
      setCurrentProject((prev) => (prev + 1) % projects.length)
    }, 800)

    return () => clearInterval(projectTimer)
  }, [])

  useEffect(() => {
    const stageTimer = setInterval(() => {
      setLoadingStage((prev) => (prev + 1) % stages.length)
    }, 1200)

    return () => clearInterval(stageTimer)
  }, [])

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-background via-primary-blue/5 to-primary-orange/5 z-50 flex items-center justify-center">
      {/* Git-like Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 text-xs font-mono text-foreground">
          git clone https://github.com/rahulseervi/portfolio.git
        </div>
        <div className="absolute bottom-20 right-20 text-xs font-mono text-foreground">
          npm run build --production
        </div>
      </div>

      <div className="text-center space-y-8 relative z-10">
        {/* Logo with Project Folder Animation */}
        <div className="relative w-32 h-32 mx-auto">
          <div className="absolute inset-0 rounded-lg border-2 border-dashed border-primary-blue/30 animate-pulse"></div>
          <div className="absolute inset-2 rounded-lg bg-card border border-border flex items-center justify-center">
            <div className="relative w-16 h-16">
              <Image src="/images/rs-logo.svg" alt="RS Logo" fill className="object-contain" />
            </div>
          </div>
          {/* Floating Icons */}
          <div className="absolute -top-2 -right-2 p-1 bg-primary-orange rounded-full animate-bounce">
            <Github className="w-3 h-3 text-white" />
          </div>
          <div className="absolute -bottom-2 -left-2 p-1 bg-primary-blue rounded-full animate-bounce delay-300">
            <ExternalLink className="w-3 h-3 text-white" />
          </div>
        </div>

        {/* Loading Text */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-foreground">Loading Projects...</h2>
          <p className="text-muted-foreground font-mono">{stages[loadingStage]}</p>

          {/* Progress Bar with Terminal Style */}
          <div className="w-96 mx-auto">
            <div className="bg-card border border-border rounded-lg p-4 font-mono text-sm">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-muted-foreground ml-2">Building...</span>
              </div>
              <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-green-500 to-blue-500 transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <div className="mt-2 text-muted-foreground">[{Math.round(progress)}%] Build progress</div>
            </div>
          </div>
        </div>

        {/* Project Cards Animation */}
        <div className="grid grid-cols-2 gap-3 max-w-sm mx-auto">
          {projects.map((project, index) => {
            const IconComponent = project.icon
            return (
              <div
                key={index}
                className={`p-3 rounded-lg border transition-all duration-300 ${
                  currentProject === index 
                    ? 'bg-primary-blue/10 border-primary-blue scale-105' 
                    : 'bg-card border-border'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <IconComponent className={`w-4 h-4 ${
                    currentProject === index ? 'text-primary-blue' : 'text-muted-foreground'
                  }`} />
                  <div className="text-left">
                    <div className="text-xs font-medium text-foreground">{project.name}</div>
                    <div className="text-xs text-muted-foreground">{project.tech}</div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default ProjectsLoadingScreen
