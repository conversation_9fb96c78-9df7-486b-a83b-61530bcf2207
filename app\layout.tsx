import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import Navbar from "@/components/Navbar"
import Footer from "@/components/Footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "<PERSON><PERSON> - Portfolio",
  description:
    "B.Tech CSE Student specializing in Big Data Analytics. Passionate about Java, React, and modern web technologies.",
  keywords: "<PERSON><PERSON>r<PERSON>, Portfolio, B.Tech CSE, Big Data Analytics, Java, React, MongoDB, Web Developer",
  authors: [{ name: "<PERSON><PERSON>" }],
  openGraph: {
    title: "<PERSON><PERSON>rvi - Portfolio",
    description: "B.Tech CSE Student specializing in Big Data Analytics",
    type: "website",
  },
  viewport: "width=device-width, initial-scale=1",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem disableTransitionOnChange>
          <div className="min-h-screen bg-background text-foreground">
            <Navbar />
            <main className="pt-16">{children}</main>
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
