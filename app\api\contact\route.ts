import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { name, email, message } = await request.json()

    // Validate input
    if (!name || !email || !message) {
      return NextResponse.json({ error: "All fields are required" }, { status: 400 })
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: "Invalid email format" }, { status: 400 })
    }

    // Here you would integrate with your email service
    // For example, using Resend, SendGrid, or Nodemailer

    // Simulating email sending
    console.log("Contact form submission:", { name, email, message })

    // You can integrate with services like:
    // - Resend: https://resend.com/
    // - SendGrid: https://sendgrid.com/
    // - EmailJS: https://www.emailjs.com/
    // - Nodemailer with SMTP

    // Example with environment variables you would set:
    // const RESEND_API_KEY = process.env.RESEND_API_KEY
    // const TO_EMAIL = process.env.TO_EMAIL || '<EMAIL>'

    // For now, we'll simulate a successful response
    await new Promise((resolve) => setTimeout(resolve, 1000)) // Simulate API delay

    return NextResponse.json({ message: "Message sent successfully!" }, { status: 200 })
  } catch (error) {
    console.error("Contact form error:", error)
    return NextResponse.json({ error: "Failed to send message. Please try again." }, { status: 500 })
  }
}
